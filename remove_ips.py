#!/usr/bin/env python3
import re

def extract_ips(text):
    # Regex pattern for IPv4 addresses
    pattern = r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'
    return re.findall(pattern, text)

def main():
    print("Enter your text (paste anything containing IP addresses):")
    user_input = input("> ")

    ips = extract_ips(user_input)

    if ips:
        print("\nExtracted IP Addresses:")
        for ip in ips:
            print(ip)
    else:
        print("\nNo IP addresses found.")

if __name__ == "__main__":
    main()
