#!/usr/bin/env python3
"""
IP Address Extractor Script

This script extracts IPv4 addresses from any text input provided by the user.
It uses regular expressions to find and display all valid IPv4 addresses
found in the input text.

Usage:
    python3 remove_ips.py

The script will prompt for text input and then display any IP addresses found.
"""

import re

def extract_ips(text):
    """
    Extract IPv4 addresses from the given text using regular expressions.

    Args:
        text (str): The input text to search for IP addresses

    Returns:
        list: A list of IPv4 addresses found in the text

    The regex pattern breakdown:
    - \b: Word boundary to ensure we match complete IP addresses
    - (?:[0-9]{1,3}\.): Non-capturing group for 1-3 digits followed by a dot
    - {3}: Repeat the above pattern exactly 3 times (for first 3 octets)
    - [0-9]{1,3}: Match 1-3 digits for the final octet
    - \b: Word boundary to ensure we match complete IP addresses

    Note: This pattern matches the format but doesn't validate if the numbers
    are within valid IP range (0-255). It will match invalid IPs like 999.999.999.999
    """
    # Regex pattern for IPv4 addresses (format validation only)
    pattern = r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'
    return re.findall(pattern, text)

def main():
    """
    Main function that handles user interaction and orchestrates the IP extraction process.

    Flow:
    1. Prompt user for text input
    2. Call extract_ips() to find IP addresses in the input
    3. Display results - either the found IPs or a "not found" message
    """
    print("Enter your text (paste anything containing IP addresses):")
    print("Examples: log files, network configs, documentation, etc.")

    # Get user input - can be multi-line text, URLs, logs, etc.
    user_input = input("> ")

    # Extract all IPv4 addresses from the input text
    ips = extract_ips(user_input)

    # Display results based on whether IPs were found
    if ips:
        print(f"\nExtracted {len(ips)} IP Address(es):")
        print("-" * 30)
        for i, ip in enumerate(ips, 1):
            print(f"{i}. {ip}")
    else:
        print("\nNo IP addresses found in the provided text.")
        print("Make sure your text contains valid IPv4 addresses (e.g., ***********)")

# Entry point - run main() when script is executed directly
if __name__ == "__main__":
    main()
