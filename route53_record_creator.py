#!/usr/bin/env python3
"""
Route53 DNS Record Creator
This script creates DNS records in Route53 based on a CSV file input
"""

import argparse
import sys
import csv
import boto3
from botocore.exceptions import ClientError, NoCredentialsError, ProfileNotFound


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Create DNS records in Route53 from CSV file"
    )
    parser.add_argument(
        "--profile", 
        default="fedramp",
        help="AWS profile to use (default: lab)"
    )
    parser.add_argument(
        "--zone",
        default="on.axoniusfed.com",
        help="DNS zone name to create records in (default: on.axoniusfed.com)"
    )
    parser.add_argument(
        "--csv-file",
        required=True,
        help="CSV file containing DNS records to create"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be created without actually creating records"
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=100,
        help="Number of records to process in each batch (default: 100)"
    )
    parser.add_argument(
        "--force",
        action="store_true",
        help="Skip confirmation prompt"
    )
    return parser.parse_args()


def create_aws_session(profile):
    """Create AWS session with specified profile"""
    try:
        session = boto3.Session(profile_name=profile)
        return session
    except ProfileNotFound:
        print(f"Error: AWS profile '{profile}' not found")
        sys.exit(1)
    except NoCredentialsError:
        print("Error: AWS credentials not found")
        sys.exit(1)


def find_hosted_zone(route53_client, zone_name):
    """Find the hosted zone ID for the given zone name"""
    try:
        # Ensure zone name ends with a dot for Route53 API
        if not zone_name.endswith('.'):
            zone_name += '.'
        
        response = route53_client.list_hosted_zones()
        
        for zone in response['HostedZones']:
            if zone['Name'] == zone_name:
                return zone['Id'].split('/')[-1]  # Extract zone ID from full path
        
        print(f"Error: Hosted zone '{zone_name}' not found")
        return None
        
    except ClientError as e:
        print(f"Error finding hosted zone: {e}")
        return None


def read_csv_records(csv_file):
    """Read DNS records from CSV file"""
    records = []
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            # Try to detect if file has headers
            sample = f.read(1024)
            f.seek(0)
            
            # Check if first line looks like headers
            first_line = sample.split('\n')[0].lower()
            has_headers = any(header in first_line for header in ['name', 'type', 'ttl', 'value'])
            
            reader = csv.reader(f)
            
            if has_headers:
                headers = next(reader)  # Skip header row
                print(f"Detected CSV headers: {headers}")
            
            for row_num, row in enumerate(reader, start=2 if has_headers else 1):
                if len(row) < 4:
                    print(f"Warning: Row {row_num} has insufficient columns, skipping: {row}")
                    continue
                
                # Clean up the values
                name = row[0].strip()
                record_type = row[1].strip().upper()
                ttl = row[2].strip()
                value = row[3].strip()
                
                # Validate TTL
                try:
                    ttl_int = int(ttl) if ttl != 'N/A' else 300
                except ValueError:
                    print(f"Warning: Invalid TTL '{ttl}' in row {row_num}, using default 300")
                    ttl_int = 300
                
                # Ensure name ends with dot if it's not already
                if not name.endswith('.'):
                    name += '.'
                
                records.append({
                    'Name': name,
                    'Type': record_type,
                    'TTL': ttl_int,
                    'Value': value,
                    'RowNumber': row_num
                })
        
        return records
        
    except FileNotFoundError:
        print(f"Error: CSV file '{csv_file}' not found")
        return []
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        return []


def validate_records(records, zone_name):
    """Validate records before creating them"""
    valid_records = []
    
    # Ensure zone name ends with dot
    if not zone_name.endswith('.'):
        zone_name += '.'
    
    for record in records:
        # Check if record name is within the zone
        if not record['Name'].endswith(zone_name):
            print(f"Warning: Record '{record['Name']}' is not in zone '{zone_name}', skipping")
            continue
        
        # Validate record type
        valid_types = ['A', 'AAAA', 'CNAME', 'MX', 'TXT', 'SRV', 'PTR', 'NS', 'SOA']
        if record['Type'] not in valid_types:
            print(f"Warning: Invalid record type '{record['Type']}' for '{record['Name']}', skipping")
            continue
        
        # Validate TTL range
        if not (1 <= record['TTL'] <= 2147483647):
            print(f"Warning: TTL {record['TTL']} out of range for '{record['Name']}', using 300")
            record['TTL'] = 300
        
        valid_records.append(record)
    
    return valid_records


def create_change_batch(records):
    """Create a Route53 change batch from records"""
    changes = []
    
    for record in records:
        change = {
            'Action': 'CREATE',
            'ResourceRecordSet': {
                'Name': record['Name'],
                'Type': record['Type'],
                'TTL': record['TTL'],
                'ResourceRecords': [{'Value': record['Value']}]
            }
        }
        changes.append(change)
    
    return {'Changes': changes}


def create_dns_records(route53_client, zone_id, records, batch_size, dry_run=False):
    """Create DNS records in Route53"""
    total_records = len(records)
    created_count = 0
    failed_count = 0
    
    print(f"Processing {total_records} records in batches of {batch_size}")
    
    # Process records in batches
    for i in range(0, total_records, batch_size):
        batch = records[i:i + batch_size]
        batch_num = (i // batch_size) + 1
        total_batches = (total_records + batch_size - 1) // batch_size
        
        print(f"\nProcessing batch {batch_num}/{total_batches} ({len(batch)} records)...")
        
        if dry_run:
            print("DRY RUN - Would create the following records:")
            for record in batch:
                print(f"  {record['Type']} {record['Name']} -> {record['Value']} (TTL: {record['TTL']})")
            created_count += len(batch)
            continue
        
        try:
            change_batch = create_change_batch(batch)
            
            response = route53_client.change_resource_record_sets(
                HostedZoneId=zone_id,
                ChangeBatch=change_batch
            )
            
            change_id = response['ChangeInfo']['Id']
            print(f"  Batch submitted successfully. Change ID: {change_id}")
            
            # Wait for change to propagate
            print("  Waiting for changes to propagate...")
            waiter = route53_client.get_waiter('resource_record_sets_changed')
            waiter.wait(Id=change_id)
            
            print(f"  ✅ Batch {batch_num} completed successfully ({len(batch)} records)")
            created_count += len(batch)
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            print(f"  ❌ Batch {batch_num} failed: {error_code} - {error_message}")
            
            # Try to process records individually if batch fails
            print("  Attempting to process records individually...")
            for record in batch:
                try:
                    individual_batch = create_change_batch([record])
                    route53_client.change_resource_record_sets(
                        HostedZoneId=zone_id,
                        ChangeBatch=individual_batch
                    )
                    print(f"    ✅ Created: {record['Type']} {record['Name']}")
                    created_count += 1
                except ClientError as individual_error:
                    print(f"    ❌ Failed: {record['Type']} {record['Name']} - {individual_error.response['Error']['Message']}")
                    failed_count += 1
    
    return created_count, failed_count


def main():
    """Main function"""
    args = parse_arguments()
    
    print(f"Route53 DNS Record Creator")
    print(f"Profile: {args.profile}")
    print(f"Zone: {args.zone}")
    print(f"CSV File: {args.csv_file}")
    print(f"Dry Run: {args.dry_run}")
    print("=" * 50)
    
    # Create AWS session
    print(f"Connecting to Route53 using profile '{args.profile}'...")
    session = create_aws_session(args.profile)
    route53_client = session.client('route53')
    
    # Find hosted zone
    print(f"Looking for hosted zone '{args.zone}'...")
    zone_id = find_hosted_zone(route53_client, args.zone)
    
    if not zone_id:
        sys.exit(1)
    
    print(f"Found hosted zone ID: {zone_id}")
    
    # Read CSV records
    print(f"Reading records from '{args.csv_file}'...")
    records = read_csv_records(args.csv_file)
    
    if not records:
        print("No records found in CSV file")
        sys.exit(1)
    
    print(f"Read {len(records)} records from CSV")
    
    # Validate records
    print("Validating records...")
    valid_records = validate_records(records, args.zone)
    
    if not valid_records:
        print("No valid records to create")
        sys.exit(1)
    
    print(f"Found {len(valid_records)} valid records to create")
    
    # Show summary of what will be created
    record_types = {}
    for record in valid_records:
        record_types[record['Type']] = record_types.get(record['Type'], 0) + 1
    
    print("\nRecord type summary:")
    for record_type, count in sorted(record_types.items()):
        print(f"  {record_type}: {count} records")
    
    # Confirmation prompt
    if not args.force and not args.dry_run:
        print(f"\nThis will create {len(valid_records)} DNS records in zone '{args.zone}'")
        response = input("Do you want to continue? (y/N): ")
        if response.lower() != 'y':
            print("Operation cancelled")
            sys.exit(0)
    
    # Create records
    created_count, failed_count = create_dns_records(
        route53_client, zone_id, valid_records, args.batch_size, args.dry_run
    )
    
    # Summary
    print(f"\n{'DRY RUN ' if args.dry_run else ''}Summary:")
    print(f"  Records processed: {len(valid_records)}")
    print(f"  Successfully created: {created_count}")
    if failed_count > 0:
        print(f"  Failed: {failed_count}")
    
    if args.dry_run:
        print("\nThis was a dry run. No actual changes were made.")
        print("Remove --dry-run flag to create the records.")


if __name__ == "__main__":
    main()
