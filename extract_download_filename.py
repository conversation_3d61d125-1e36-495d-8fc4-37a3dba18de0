#!/usr/bin/env python3
"""
Script to extract filename from download command and perform the download
"""

import re
import requests
import argparse
import sys
import os
from urllib.parse import urlparse


def extract_filename_from_bash_command(bash_command):
    """Extract the output filename from the bash command"""
    # Look for the -o flag followed by the filename
    pattern = r'curl\s+.*?-o\s+"([^"]+)"'
    match = re.search(pattern, bash_command)
    
    if match:
        return match.group(1)
    
    # Alternative pattern without quotes
    pattern = r'curl\s+.*?-o\s+(\S+)'
    match = re.search(pattern, bash_command)
    
    if match:
        return match.group(1)
    
    return None


def get_download_url(api_url, token):
    """Get the actual download URL from the API"""
    headers = {
        'X-Artifact-Token': token
    }
    
    try:
        response = requests.get(api_url, headers=headers)
        response.raise_for_status()
        
        # The API should return the download URL
        download_url = response.text.strip()
        
        # Validate that it's a proper URL
        if download_url.startswith(('http://', 'https://')):
            return download_url
        else:
            print(f"Error: Invalid response from API: {download_url}")
            return None
            
    except requests.RequestException as e:
        print(f"Error getting download URL: {e}")
        return None


def download_file(url, filename):
    """Download file from URL"""
    try:
        print(f"Downloading {filename} from {url}")
        
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        # Get total file size if available
        total_size = int(response.headers.get('content-length', 0))
        
        with open(filename, 'wb') as f:
            downloaded = 0
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    
                    # Show progress if we know the total size
                    if total_size > 0:
                        percent = (downloaded / total_size) * 100
                        print(f"\rProgress: {percent:.1f}% ({downloaded}/{total_size} bytes)", end='', flush=True)
        
        print(f"\nDownload completed: {filename}")
        return True
        
    except requests.RequestException as e:
        print(f"Error downloading file: {e}")
        return False


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Extract filename and download from bash command")
    parser.add_argument("--command", help="The bash command to parse")
    parser.add_argument("--download", action="store_true", help="Actually perform the download")
    
    args = parser.parse_args()
    
    # Default bash command if none provided
    default_command = '''bash -c 'URL=$(curl -s -H "X-Artifact-Token: 30797339-a61e-43c3-9ce0-e10f178ffe13" https://share.axonius.com/api/files/159743ba-9315-4ad7-87d3-0db2760a5d46/download-url); [[ $URL =~ ^https?:// ]] && curl -o "axonius_Ax-OS-1.7.9.sh" "$URL" || echo "Error: Invalid response: $URL"' '''
    
    command = args.command if args.command else default_command
    
    print("Analyzing bash command:")
    print(f"Command: {command}")
    print()
    
    # Extract filename
    filename = extract_filename_from_bash_command(command)
    if filename:
        print(f"Extracted filename: {filename}")
    else:
        print("Could not extract filename from command")
        return
    
    # Extract API URL and token
    api_pattern = r'curl\s+-s\s+-H\s+"X-Artifact-Token:\s*([^"]+)"\s+([^\s;]+)'
    api_match = re.search(api_pattern, command)
    
    if api_match:
        token = api_match.group(1)
        api_url = api_match.group(2)
        print(f"API URL: {api_url}")
        print(f"Token: {token[:10]}...")
        
        if args.download:
            print("\nGetting download URL...")
            download_url = get_download_url(api_url, token)
            
            if download_url:
                print(f"Download URL: {download_url}")
                
                # Check if file already exists
                if os.path.exists(filename):
                    response = input(f"File {filename} already exists. Overwrite? (y/N): ")
                    if response.lower() != 'y':
                        print("Download cancelled.")
                        return
                
                # Download the file
                success = download_file(download_url, filename)
                
                if success:
                    file_size = os.path.getsize(filename)
                    print(f"File downloaded successfully: {filename} ({file_size} bytes)")
                else:
                    print("Download failed.")
            else:
                print("Could not get download URL.")
        else:
            print("\nUse --download flag to actually download the file")
    else:
        print("Could not extract API URL and token from command")


if __name__ == "__main__":
    main()
