#!/usr/bin/env python3
"""
DNS Record Checker
This script checks DNS records in DNS
"""
import argparse
import dns.resolver
import sys


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Check DNS records for a domain"
    )
    parser.add_argument(
        "domain",
        help="Domain name to query"
    )
    parser.add_argument(
        "--record-type",
        default="SOA",
        help="DNS record type to query (default: SOA)"
    )
    return parser.parse_args()


def main():
    """Main function"""
    args = parse_arguments()

    try:
        answer = dns.resolver.resolve(args.domain, args.record_type)

        print(f"DNS {args.record_type} records for {args.domain}:")
        print("=" * 50)

        if args.record_type.upper() == 'SOA':
            for rdata in answer:
                print("Serial:", rdata.serial)
                print("Refresh:", rdata.refresh)
                print("Retry:", rdata.retry)
                print("Expire:", rdata.expire)
                print("Minimum TTL:", rdata.minimum)
                print("Primary NS:", rdata.mname)
                print("Responsible Email:", rdata.rname)
        else:
            for rdata in answer:
                print(rdata)

    except dns.resolver.NXDOMAIN:
        print(f"Error: Domain '{args.domain}' does not exist")
        sys.exit(1)
    except dns.resolver.NoAnswer:
        print(f"Error: No {args.record_type} records found for '{args.domain}'")
        sys.exit(1)
    except Exception as e:
        print(f"Error querying DNS: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
