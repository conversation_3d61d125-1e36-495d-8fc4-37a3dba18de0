#!/usr/bin/env python3
"""
Route53 CNAME DNS Entries Extractor
This script pulls all CNAME DNS entries from Route53 for a specified zone using AWS profile
"""

import argparse
import sys
import json
import csv
import boto3
from botocore.exceptions import ClientError, NoCredentialsError, ProfileNotFound


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Extract CNAME DNS entries from Route53 hosted zone"
    )
    parser.add_argument(
        "--profile", 
        default="lab",
        help="AWS profile to use (default: lab)"
    )
    parser.add_argument(
        "--zone",
        default="on.axoniusfed.com",
        help="DNS zone name to query (default: on.axoniusfed.com)"
    )
    parser.add_argument(
        "--output-format",
        choices=["json", "csv", "table"],
        default="table",
        help="Output format (default: table)"
    )
    parser.add_argument(
        "--output-file",
        help="Output file path (optional, prints to stdout if not specified)"
    )
    parser.add_argument(
        "--include-all-types",
        action="store_true",
        help="Include all record types, not just CNAME records"
    )
    return parser.parse_args()


def create_aws_session(profile):
    """Create AWS session with specified profile"""
    try:
        session = boto3.Session(profile_name=profile)
        return session
    except ProfileNotFound:
        print(f"Error: AWS profile '{profile}' not found")
        sys.exit(1)
    except NoCredentialsError:
        print("Error: AWS credentials not found")
        sys.exit(1)


def find_hosted_zone(route53_client, zone_name):
    """Find the hosted zone ID for the given zone name"""
    try:
        # Ensure zone name ends with a dot for Route53 API
        if not zone_name.endswith('.'):
            zone_name += '.'
        
        response = route53_client.list_hosted_zones()
        
        for zone in response['HostedZones']:
            if zone['Name'] == zone_name:
                return zone['Id'].split('/')[-1]  # Extract zone ID from full path
        
        print(f"Error: Hosted zone '{zone_name}' not found")
        return None
        
    except ClientError as e:
        print(f"Error finding hosted zone: {e}")
        return None


def get_dns_records(route53_client, zone_id, include_all_types=False):
    """Get DNS records from the hosted zone"""
    try:
        records = []
        paginator = route53_client.get_paginator('list_resource_record_sets')
        
        for page in paginator.paginate(HostedZoneId=zone_id):
            for record_set in page['ResourceRecordSets']:
                record_type = record_set['Type']
                
                # Filter for CNAME records unless include_all_types is True
                if not include_all_types and record_type != 'CNAME':
                    continue
                
                record_name = record_set['Name']
                ttl = record_set.get('TTL', 'N/A')
                
                # Handle different record value formats
                values = []
                if 'ResourceRecords' in record_set:
                    values = [rr['Value'] for rr in record_set['ResourceRecords']]
                elif 'AliasTarget' in record_set:
                    alias_target = record_set['AliasTarget']
                    values = [f"ALIAS -> {alias_target['DNSName']} (Zone: {alias_target['HostedZoneId']})"]
                
                for value in values:
                    records.append({
                        'Name': record_name,
                        'Type': record_type,
                        'TTL': ttl,
                        'Value': value
                    })
        
        return records
        
    except ClientError as e:
        print(f"Error retrieving DNS records: {e}")
        return []


def format_output(records, output_format):
    """Format records according to specified output format"""
    if output_format == "json":
        return json.dumps(records, indent=2)
    
    elif output_format == "csv":
        if not records:
            return "Name,Type,TTL,Value\n"
        
        output = "Name,Type,TTL,Value\n"
        for record in records:
            # Escape commas in values for CSV
            name = record['Name'].replace(',', '\\,')
            value = record['Value'].replace(',', '\\,')
            output += f"{name},{record['Type']},{record['TTL']},{value}\n"
        return output
    
    elif output_format == "table":
        if not records:
            return "No records found.\n"
        
        # Calculate column widths
        max_name = max(len(record['Name']) for record in records)
        max_type = max(len(record['Type']) for record in records)
        max_ttl = max(len(str(record['TTL'])) for record in records)
        max_value = max(len(record['Value']) for record in records)
        
        # Ensure minimum column widths
        max_name = max(max_name, 4)  # "Name"
        max_type = max(max_type, 4)  # "Type"
        max_ttl = max(max_ttl, 3)    # "TTL"
        max_value = max(max_value, 5) # "Value"
        
        # Create header
        header = f"{'Name':<{max_name}} | {'Type':<{max_type}} | {'TTL':<{max_ttl}} | {'Value':<{max_value}}"
        separator = "-" * len(header)
        
        output = f"{header}\n{separator}\n"
        
        # Add records
        for record in records:
            output += f"{record['Name']:<{max_name}} | {record['Type']:<{max_type}} | {str(record['TTL']):<{max_ttl}} | {record['Value']:<{max_value}}\n"
        
        return output


def main():
    """Main function"""
    args = parse_arguments()
    
    print(f"Connecting to Route53 using profile '{args.profile}'...")
    
    # Create AWS session
    session = create_aws_session(args.profile)
    route53_client = session.client('route53')
    
    # Find hosted zone
    print(f"Looking for hosted zone '{args.zone}'...")
    zone_id = find_hosted_zone(route53_client, args.zone)
    
    if not zone_id:
        sys.exit(1)
    
    print(f"Found hosted zone ID: {zone_id}")
    
    # Get DNS records
    record_type_desc = "all DNS records" if args.include_all_types else "CNAME records"
    print(f"Retrieving {record_type_desc}...")
    
    records = get_dns_records(route53_client, zone_id, args.include_all_types)
    
    if not records:
        print(f"No {record_type_desc} found in zone '{args.zone}'")
        sys.exit(0)
    
    print(f"Found {len(records)} {record_type_desc}")
    
    # Format output
    formatted_output = format_output(records, args.output_format)
    
    # Write to file or stdout
    if args.output_file:
        try:
            with open(args.output_file, 'w', encoding='utf-8') as f:
                f.write(formatted_output)
            print(f"Output written to: {args.output_file}")
        except IOError as e:
            print(f"Error writing to file: {e}")
            sys.exit(1)
    else:
        print(f"\n{record_type_desc.title()} for zone '{args.zone}':")
        print("=" * 50)
        print(formatted_output)


if __name__ == "__main__":
    main()
