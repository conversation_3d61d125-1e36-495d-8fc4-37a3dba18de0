#!/usr/bin/env python3
"""
Script to download Azure ServiceTags JSON and extract IP addresses to CSV
"""

import json
import csv
import requests
from urllib.parse import urlparse
import sys
import os

def download_json(url):
    """Download JSON data from URL"""
    print(f"Downloading data from: {url}")
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error downloading data: {e}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON: {e}")
        sys.exit(1)

def extract_ips_to_csv(data, output_file):
    """Extract IP addresses from ServiceTags data and write to CSV"""
    print(f"Extracting IP addresses to: {output_file}")
    
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        
        # Write header
        writer.writerow(['Service', 'Region', 'IP_Address', 'Change_Number'])
        
        # Extract change number from top level
        change_number = data.get('changeNumber', 'Unknown')
        
        # Process each service
        for service in data.get('values', []):
            service_name = service.get('name', 'Unknown')
            service_id = service.get('id', 'Unknown')
            
            # Extract region from service name or ID
            region = 'Global'
            if '.' in service_name:
                parts = service_name.split('.')
                if len(parts) > 1:
                    region = parts[-1]
            
            # Extract IP addresses
            properties = service.get('properties', {})
            address_prefixes = properties.get('addressPrefixes', [])
            
            for ip_address in address_prefixes:
                writer.writerow([service_name, region, ip_address, change_number])
    
    print(f"Successfully extracted IP addresses to {output_file}")

def main():
    # URL for Azure ServiceTags
    url = "https://download.microsoft.com/download/7/1/d/71d86715-5596-4529-9b13-da13a5de5b63/ServiceTags_Public_20250929.json"
    
    # Output CSV file
    output_file = "azure_ips.csv"
    
    # Download and parse JSON
    data = download_json(url)
    
    # Extract IPs to CSV
    extract_ips_to_csv(data, output_file)
    
    # Print summary
    print(f"\nSummary:")
    print(f"- Total services: {len(data.get('values', []))}")
    print(f"- Change number: {data.get('changeNumber', 'Unknown')}")
    print(f"- Output file: {output_file}")
    
    # Count total IPs
    total_ips = 0
    for service in data.get('values', []):
        properties = service.get('properties', {})
        address_prefixes = properties.get('addressPrefixes', [])
        total_ips += len(address_prefixes)
    
    print(f"- Total IP addresses: {total_ips}")

if __name__ == "__main__":
    main()
