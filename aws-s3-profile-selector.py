#!/usr/bin/env python3
"""
AWS S3 Profile and Region Selector Script
This script provides an interactive way to select AWS profiles and regions,
then runs 'aws s3 ls' with the selected configuration.
"""

import argparse
import sys
import subprocess
import os
import configparser
import boto3
from botocore.exceptions import ClientError, NoCredentialsError, ProfileNotFound
from pick import pick


def get_available_profiles():
    """Get list of available AWS profiles from credentials and config files"""
    profiles = set()
    
    # Check AWS credentials file
    credentials_path = os.path.expanduser('~/.aws/credentials')
    if os.path.exists(credentials_path):
        config = configparser.ConfigParser()
        config.read(credentials_path)
        profiles.update(config.sections())
    
    # Check AWS config file
    config_path = os.path.expanduser('~/.aws/config')
    if os.path.exists(config_path):
        config = configparser.ConfigParser()
        config.read(config_path)
        for section in config.sections():
            if section.startswith('profile '):
                profile_name = section.replace('profile ', '')
                profiles.add(profile_name)
    
    return sorted(list(profiles))


def get_available_regions():
    """Get list of common AWS regions"""
    return [
        'us-east-1',
        'us-east-2', 
        'us-west-1',
        'us-west-2',
        'us-gov-east-1',
        'us-gov-west-1',
        'eu-west-1',
        'eu-west-2',
        'eu-central-1',
        'ap-southeast-1',
        'ap-southeast-2',
        'ap-northeast-1',
        'ca-central-1',
        'sa-east-1'
    ]


def validate_profile(profile_name):
    """Validate that the AWS profile exists and has valid credentials"""
    try:
        session = boto3.Session(profile_name=profile_name)
        # Try to get caller identity to validate credentials
        sts = session.client('sts')
        sts.get_caller_identity()
        return True
    except (ProfileNotFound, NoCredentialsError, ClientError) as e:
        print(f"Warning: Profile '{profile_name}' validation failed: {e}")
        return False


def run_aws_s3_ls(profile, region, path=""):
    """Run aws s3 ls command with specified profile and region"""
    try:
        cmd = ['aws', 's3', 'ls']
        
        if path:
            cmd.append(path)
            
        cmd.extend(['--profile', profile, '--region', region])
        
        print(f"\nRunning: {' '.join(cmd)}")
        print("-" * 50)
        
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        
        if result.stdout:
            print(result.stdout)
        else:
            print("No output returned from aws s3 ls")
            
        if result.stderr:
            print(f"Warnings/Errors: {result.stderr}")
            
    except subprocess.CalledProcessError as e:
        print(f"Error running AWS S3 command: {e}")
        if e.stderr:
            print(f"Error details: {e.stderr.decode()}")
        return False
    except FileNotFoundError:
        print("Error: AWS CLI not found. Please install the AWS CLI.")
        return False
    
    return True


def interactive_profile_selection():
    """Interactive profile selection menu"""
    profiles = get_available_profiles()
    
    if not profiles:
        print("No AWS profiles found. Please configure AWS CLI first.")
        print("Run: aws configure --profile <profile-name>")
        sys.exit(1)
    
    print(f"Found {len(profiles)} AWS profiles")
    
    # Add profile validation info
    validated_profiles = []
    for profile in profiles:
        status = "✓" if validate_profile(profile) else "⚠"
        validated_profiles.append(f"{profile} {status}")
    
    try:
        title = "Select an AWS profile (✓ = validated, ⚠ = validation failed):"
        selected_option, selected_index = pick(validated_profiles, title, indicator='=>')
        selected_profile = profiles[selected_index]
        return selected_profile
    except KeyboardInterrupt:
        print("\nCancelled.")
        sys.exit(0)


def interactive_region_selection():
    """Interactive region selection menu"""
    regions = get_available_regions()
    
    try:
        title = "Select an AWS region:"
        selected_region, _ = pick(regions, title, indicator='=>')
        return selected_region
    except KeyboardInterrupt:
        print("\nCancelled.")
        sys.exit(0)


def interactive_s3_path_input():
    """Get S3 path from user input"""
    print("\nEnter S3 path (optional, press Enter for root listing):")
    print("Examples:")
    print("  - Press Enter for: aws s3 ls")
    print("  - s3://my-bucket/ for: aws s3 ls s3://my-bucket/")
    print("  - s3://my-bucket/folder/ for: aws s3 ls s3://my-bucket/folder/")
    
    try:
        s3_path = input("S3 path: ").strip()
        return s3_path
    except KeyboardInterrupt:
        print("\nCancelled.")
        sys.exit(0)


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Interactive AWS S3 listing with profile and region selection"
    )
    parser.add_argument(
        "--profile",
        help="AWS profile to use (if not specified, interactive selection will be shown)"
    )
    parser.add_argument(
        "--region", 
        help="AWS region to use (if not specified, interactive selection will be shown)"
    )
    parser.add_argument(
        "--path",
        help="S3 path to list (if not specified, interactive input will be shown)"
    )
    parser.add_argument(
        "--non-interactive",
        action="store_true",
        help="Run in non-interactive mode (requires --profile and --region)"
    )
    return parser.parse_args()


def main():
    """Main function"""
    args = parse_arguments()
    
    # Handle non-interactive mode
    if args.non_interactive:
        if not args.profile or not args.region:
            print("Error: --non-interactive mode requires both --profile and --region")
            sys.exit(1)
        profile = args.profile
        region = args.region
        s3_path = args.path or ""
    else:
        # Interactive mode
        profile = args.profile or interactive_profile_selection()
        region = args.region or interactive_region_selection()
        s3_path = args.path if args.path is not None else interactive_s3_path_input()
    
    print(f"\nSelected configuration:")
    print(f"  Profile: {profile}")
    print(f"  Region: {region}")
    print(f"  S3 Path: {s3_path or '(root listing)'}")
    
    # Run the AWS S3 command
    success = run_aws_s3_ls(profile, region, s3_path)
    
    if not success:
        sys.exit(1)
    
    # Ask if user wants to run another command
    if not args.non_interactive:
        print("\n" + "="*50)
        try:
            again = input("Run another S3 command? (y/N): ").strip().lower()
            if again in ['y', 'yes']:
                main()  # Recursive call for another run
        except KeyboardInterrupt:
            print("\nExiting...")


if __name__ == "__main__":
    main()
