# AWS S3 Profile Selector

An interactive Python script that allows you to select AWS profiles and regions, then runs `aws s3 ls` with the selected configuration.

## Features

- **Interactive Profile Selection**: Lists all available AWS profiles from your `~/.aws/credentials` and `~/.aws/config` files
- **Profile Validation**: Shows which profiles have valid credentials (✓) and which have issues (⚠)
- **Interactive Region Selection**: Choose from common AWS regions including GovCloud
- **Flexible S3 Path Input**: List root buckets or specify specific bucket/folder paths
- **Non-Interactive Mode**: Can be run with command-line arguments for automation
- **Recursive Operation**: Option to run multiple commands in sequence

## Prerequisites

- AWS CLI installed and configured
- Python 3.6+
- Required Python packages (install with `pip3 install -r requirements.txt`):
  - `boto3>=1.26.0`
  - `pick>=2.2.0`

## Usage

### Interactive Mode (Default)

```bash
# Run with full interactive selection
python3 aws-s3-profile-selector.py

# Pre-select profile but choose region interactively
python3 aws-s3-profile-selector.py --profile lab

# Pre-select profile and region but choose path interactively
python3 aws-s3-profile-selector.py --profile lab --region us-gov-west-1
```

### Non-Interactive Mode

```bash
# List all buckets
python3 aws-s3-profile-selector.py --profile lab --region us-gov-west-1 --non-interactive

# List contents of specific bucket
python3 aws-s3-profile-selector.py --profile lab --region us-gov-west-1 --path s3://my-bucket/ --non-interactive

# List contents of specific folder
python3 aws-s3-profile-selector.py --profile lab --region us-gov-west-1 --path s3://my-bucket/folder/ --non-interactive
```

## Interactive Flow

1. **Profile Selection**: Choose from available AWS profiles with validation status
2. **Region Selection**: Choose from common AWS regions
3. **S3 Path Input**: Enter S3 path or press Enter for root listing
4. **Command Execution**: The script runs `aws s3 ls` with your selections
5. **Repeat Option**: Choose to run another command or exit

## Examples

### Example 1: List all buckets
```
Selected configuration:
  Profile: lab
  Region: us-gov-west-1
  S3 Path: (root listing)

Running: aws s3 ls --profile lab --region us-gov-west-1
```

### Example 2: List bucket contents
```
Selected configuration:
  Profile: production
  Region: us-east-1
  S3 Path: s3://my-data-bucket/

Running: aws s3 ls s3://my-data-bucket/ --profile production --region us-east-1
```

## Command Line Arguments

- `--profile PROFILE`: AWS profile to use (skips interactive profile selection)
- `--region REGION`: AWS region to use (skips interactive region selection)  
- `--path PATH`: S3 path to list (skips interactive path input)
- `--non-interactive`: Run without any interactive prompts (requires --profile and --region)
- `--help`: Show help message

## Profile Validation

The script validates each profile by attempting to call `sts:GetCallerIdentity`. Profiles are marked as:
- ✓ **Validated**: Profile has working credentials
- ⚠ **Warning**: Profile exists but credentials may be invalid or expired

## Error Handling

- Missing AWS CLI: Script will exit with helpful error message
- Invalid profiles: Warning displayed but profile can still be selected
- AWS API errors: Detailed error messages shown
- Keyboard interrupts: Graceful exit with cleanup

## Integration with Existing Scripts

This script follows the same patterns as your existing `aws-ssm-connect.py` script:
- Uses the same dependencies (`boto3`, `pick`)
- Similar argument parsing and session management
- Consistent error handling and user experience
