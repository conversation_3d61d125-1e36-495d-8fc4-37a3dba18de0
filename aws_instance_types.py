#!/usr/bin/env python3
import boto3
import pandas as pd

# Initialize EC2 client with lab profile and us-gov-west-1 region
session = boto3.Session(profile_name='lab', region_name='us-gov-west-1')
ec2 = session.client('ec2')

# Get all instance types
response = ec2.describe_instance_types()
instance_types = response['InstanceTypes']

# Continue paginating if there are more instance types
while 'NextToken' in response:
    response = ec2.describe_instance_types(NextToken=response['NextToken'])
    instance_types.extend(response['InstanceTypes'])

# Extract relevant data and create a list of dictionaries
instance_data = []
for instance in instance_types:
    data = {
        'InstanceType': instance['InstanceType'],
        'vCPUs': instance['VCpuInfo']['DefaultVCpus'],
        'MemoryGiB': instance['MemoryInfo']['SizeInMiB'] / 1024,
        'InstanceFamily': instance['InstanceFamily'],
        'NetworkPerformance': instance.get('NetworkInfo', {}).get('NetworkPerformance')
    }
    instance_data.append(data)

# Create a Pandas DataFrame
df = pd.DataFrame(instance_data)

# Save to Excel
df.to_excel('aws_ec2_instance_types.xlsx', index=False)

print("aws_ec2_instance_types.xlsx created successfully.")
